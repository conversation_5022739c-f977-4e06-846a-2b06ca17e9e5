# Schéma zapojení automatického řízení pohybu stolu

## Mermaid diagram kód

```mermaid
flowchart TD
    %% Vstupní svorky
    I1[I1<br/>Start]
    I2[I2<br/><PERSON><PERSON> spínač]
    I3[I3<br/><PERSON><PERSON>vý spínač]
    I4[I4<br/>Stop]
    
    %% RS klopné obvody
    B001["B001<br/>RS<br/>S ┌─┐ Q<br/>R └─┘"]
    B002["B002<br/>RS<br/>S ┌─┐ Q<br/>R └─┘"]
    
    %% Logické obvody
    B003["B003<br/>AND<br/>& "]
    B004["B004<br/>AND<br/>& "]
    B005["B005<br/>NOT<br/>1"]
    
    %% Výstupní bloky
    Q1[Q1<br/>Motor vlevo]
    Q2[Q2<br/>Motor vpravo]
    
    %% Zapojení paměti START/STOP
    I1 --> B001
    I4 --> B001
    
    %% Zapojení paměti SMĚR
    I2 --> B002
    I3 --> B002
    
    %% Zapojení pro pohyb vpravo (Q2)
    B001 --> B004
    B002 --> B004
    B004 --> Q2
    
    %% Zapojení pro pohyb vlevo (Q1)
    B001 --> B003
    B002 --> B005
    B005 --> B003
    B003 --> Q1
    
    %% Styly pro lepší vizualizaci
    classDef inputStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef rsStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef logicStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef outputStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    
    class I1,I2,I3,I4 inputStyle
    class B001,B002 rsStyle
    class B003,B004,B005 logicStyle
    class Q1,Q2 outputStyle
```

## Popis zapojení

### Vstupní svorky (modré):
- **I1** - Start (spouští systém)
- **I2** - Levý spínač (koncový spínač vlevo)
- **I3** - Pravý spínač (koncový spínač vpravo)  
- **I4** - Stop (zastavuje systém)

### RS klopné obvody (oranžové):
- **B001** - Paměť START/STOP (I1→S, I4→R)
- **B002** - Paměť směru pohybu (I2→S, I3→R)

### Logické obvody (fialové):
- **B003** - AND pro pohyb vlevo
- **B004** - AND pro pohyb vpravo
- **B005** - NOT pro negaci směru

### Výstupní bloky (zelené):
- **Q1** - Motor vlevo
- **Q2** - Motor vpravo

## Logika fungování:

1. **Spuštění:** Tlačítko START (I1) nastaví paměť B001
2. **Zastavení:** Tlačítko STOP (I4) vynuluje paměť B001
3. **Směr:** Koncové spínače I2/I3 řídí paměť směru B002
4. **Pohyb vpravo (Q2):** Aktivní když systém běží AND směr je vpravo
5. **Pohyb vlevo (Q1):** Aktivní když systém běží AND směr NENÍ vpravo (negace přes B005)

## Jak použít:

1. Zkopírujte Mermaid kód výše
2. Vložte ho do online editoru Mermaid (https://mermaid.live/)
3. Exportujte jako PNG nebo SVG obrázek
4. Nebo použijte v dokumentaci podporující Mermaid diagramy
